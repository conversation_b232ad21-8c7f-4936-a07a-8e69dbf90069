import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useMainStore = defineStore('main', () => {
  // State
  const sidebarCollapsed = ref(false)
  const darkMode = ref(false)
  const notifications = ref([
    {
      id: 1,
      title: 'Congratulation Lettie 🎉',
      message: 'Won the monthly best seller gold badge',
      time: '1h ago',
      avatar: 'https://ui-avatars.com/api/?name=Lettie&background=f093fb&color=fff',
      read: false
    },
    {
      id: 2,
      title: '<PERSON>',
      message: 'Accepted your connection',
      time: '12hr ago',
      avatar: null,
      initials: 'CF',
      read: false
    },
    {
      id: 3,
      title: 'New Message ✉️',
      message: 'You have new message from <PERSON>',
      time: '1h ago',
      avatar: 'https://ui-avatars.com/api/?name=Natalie&background=4facfe&color=fff',
      read: false
    }
  ])

  const user = ref({
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=667eea&color=fff'
  })

  // Getters
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read)
  )

  const unreadCount = computed(() => unreadNotifications.value.length)

  // Actions
  function toggleSidebar() {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  function setSidebarCollapsed(collapsed) {
    sidebarCollapsed.value = collapsed
  }

  function toggleDarkMode() {
    darkMode.value = !darkMode.value
    // Apply dark mode class to document
    if (darkMode.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
    // Save to localStorage
    localStorage.setItem('darkMode', darkMode.value.toString())
  }

  function initializeDarkMode() {
    const saved = localStorage.getItem('darkMode')
    if (saved !== null) {
      darkMode.value = saved === 'true'
    } else {
      // Check system preference
      darkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    
    if (darkMode.value) {
      document.documentElement.classList.add('dark')
    }
  }

  function markNotificationAsRead(id) {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  function markAllNotificationsAsRead() {
    notifications.value.forEach(n => n.read = true)
  }

  function addNotification(notification) {
    notifications.value.unshift({
      id: Date.now(),
      read: false,
      time: 'now',
      ...notification
    })
  }

  return {
    // State
    sidebarCollapsed,
    darkMode,
    notifications,
    user,
    
    // Getters
    unreadNotifications,
    unreadCount,
    
    // Actions
    toggleSidebar,
    setSidebarCollapsed,
    toggleDarkMode,
    initializeDarkMode,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    addNotification
  }
})
