// Application constants
export const APP_NAME = 'Vuexy Admin Dashboard'
export const APP_VERSION = '1.0.0'

// Theme constants
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
}

// Color palettes
export const COLORS = {
  PRIMARY: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  SUCCESS: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  WARNING: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },
  DANGER: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  }
}

// Breakpoints
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
}

// Chart colors
export const CHART_COLORS = [
  '#667eea',
  '#764ba2', 
  '#f093fb',
  '#f5576c',
  '#4facfe',
  '#00f2fe',
  '#fa709a',
  '#fee140'
]

// Navigation menu items
export const MENU_ITEMS = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: 'HomeIcon',
    path: '/',
    badge: null
  },
  {
    id: 'apps',
    title: 'Apps & Pages',
    icon: 'CubeIcon',
    children: [
      { id: 'email', title: 'Email', path: '/apps/email' },
      { id: 'chat', title: 'Chat', path: '/apps/chat' },
      { id: 'calendar', title: 'Calendar', path: '/apps/calendar' },
      { id: 'kanban', title: 'Kanban', path: '/apps/kanban' }
    ]
  },
  {
    id: 'ecommerce',
    title: 'eCommerce',
    icon: 'ShoppingCartIcon',
    children: [
      { id: 'ecommerce-dashboard', title: 'Dashboard', path: '/ecommerce/dashboard' },
      { id: 'products', title: 'Products', path: '/ecommerce/products' },
      { id: 'orders', title: 'Orders', path: '/ecommerce/orders' }
    ]
  },
  {
    id: 'users',
    title: 'Users',
    icon: 'UsersIcon',
    children: [
      { id: 'users-list', title: 'List', path: '/users/list' }
    ]
  },
  {
    id: 'components',
    title: 'Components',
    icon: 'PuzzlePieceIcon',
    children: [
      { id: 'cards', title: 'Cards', path: '/components/cards' },
      { id: 'buttons', title: 'Buttons', path: '/components/buttons' },
      { id: 'modals', title: 'Modals', path: '/components/modals' }
    ]
  },
  {
    id: 'forms',
    title: 'Forms & Tables',
    icon: 'DocumentIcon',
    children: [
      { id: 'basic-inputs', title: 'Basic Inputs', path: '/forms/basic-inputs' },
      { id: 'datatables', title: 'DataTables', path: '/tables/datatables' }
    ]
  },
  {
    id: 'charts',
    title: 'Charts',
    icon: 'ChartBarIcon',
    path: '/charts/apex'
  }
]

// Status types
export const STATUS_TYPES = {
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  INFO: 'info'
}

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'vuexy_theme',
  SIDEBAR_COLLAPSED: 'vuexy_sidebar_collapsed',
  USER_PREFERENCES: 'vuexy_user_preferences'
}
