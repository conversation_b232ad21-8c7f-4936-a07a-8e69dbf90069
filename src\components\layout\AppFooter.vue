<template>
  <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 px-4 lg:px-6">
    <div class="flex flex-col sm:flex-row items-center justify-between text-sm text-gray-500 dark:text-gray-400">
      <div class="flex items-center space-x-1">
        <span>©</span>
        <span>{{ currentYear }}, made with</span>
        <HeartIcon class="w-4 h-4 text-red-500" />
        <span>by</span>
        <a href="https://pixinvent.com" class="text-primary-600 dark:text-primary-400 hover:underline">
          Pixinvent
        </a>
      </div>
      
      <div class="flex items-center space-x-4 mt-2 sm:mt-0">
        <a href="#" class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
          License
        </a>
        <a href="#" class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
          More Themes
        </a>
        <a href="#" class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
          Documentation
        </a>
        <a href="#" class="hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
          Support
        </a>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { HeartIcon } from '@heroicons/vue/24/solid'

const currentYear = computed(() => new Date().getFullYear())
</script>
