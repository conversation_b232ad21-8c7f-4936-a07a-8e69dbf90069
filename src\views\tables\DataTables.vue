<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">DataTables</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Advanced data tables with sorting and filtering</p>
    </div>

    <BaseCard title="DataTable Components" subtitle="Coming Soon">
      <div class="text-center py-12">
        <TableCellsIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">DataTable Components</h3>
        <p class="text-gray-500 dark:text-gray-400">
          DataTable component library is under development. Check back soon!
        </p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import BaseCard from '../../components/ui/BaseCard.vue'
import { TableCellsIcon } from '@heroicons/vue/24/outline'
</script>
