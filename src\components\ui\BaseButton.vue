<template>
  <component
    :is="tag"
    :type="tag === 'button' ? type : undefined"
    :href="tag === 'a' ? href : undefined"
    :to="tag === 'router-link' ? to : undefined"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="handleClick"
  >
    <!-- Loading spinner -->
    <svg
      v-if="loading"
      class="animate-spin -ml-1 mr-2 h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>

    <!-- Left icon -->
    <component
      v-if="leftIcon && !loading"
      :is="leftIcon"
      :class="iconClasses"
    />

    <!-- Button text -->
    <span v-if="$slots.default || text">
      <slot>{{ text }}</slot>
    </span>

    <!-- Right icon -->
    <component
      v-if="rightIcon && !loading"
      :is="rightIcon"
      :class="iconClasses"
    />
  </component>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // Content
  text: {
    type: String,
    default: ''
  },
  
  // Appearance
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => [
      'primary', 'secondary', 'success', 'warning', 'danger', 'info',
      'outline-primary', 'outline-secondary', 'outline-success', 
      'outline-warning', 'outline-danger', 'outline-info',
      'ghost', 'link'
    ].includes(value)
  },
  
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  
  // Icons
  leftIcon: {
    type: [Object, Function],
    default: null
  },
  
  rightIcon: {
    type: [Object, Function],
    default: null
  },
  
  // States
  loading: {
    type: Boolean,
    default: false
  },
  
  disabled: {
    type: Boolean,
    default: false
  },
  
  // HTML attributes
  type: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value)
  },
  
  // Navigation
  tag: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'a', 'router-link'].includes(value)
  },
  
  href: {
    type: String,
    default: null
  },
  
  to: {
    type: [String, Object],
    default: null
  },
  
  // Layout
  block: {
    type: Boolean,
    default: false
  },
  
  rounded: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const buttonClasses = computed(() => {
  const classes = [
    'inline-flex items-center justify-center font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'
  ]
  
  // Size classes
  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
    xl: 'px-8 py-4 text-lg'
  }
  classes.push(sizeClasses[props.size])
  
  // Variant classes
  const variantClasses = {
    primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
    warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    info: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    
    'outline-primary': 'border border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500',
    'outline-secondary': 'border border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500',
    'outline-success': 'border border-green-600 text-green-600 hover:bg-green-50 focus:ring-green-500',
    'outline-warning': 'border border-yellow-600 text-yellow-600 hover:bg-yellow-50 focus:ring-yellow-500',
    'outline-danger': 'border border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500',
    'outline-info': 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',
    
    ghost: 'text-gray-600 hover:bg-gray-100 focus:ring-gray-500',
    link: 'text-primary-600 hover:text-primary-700 hover:underline focus:ring-primary-500'
  }
  classes.push(variantClasses[props.variant])
  
  // Border radius
  if (props.rounded) {
    classes.push('rounded-full')
  } else {
    classes.push('rounded-lg')
  }
  
  // Block width
  if (props.block) {
    classes.push('w-full')
  }
  
  return classes.join(' ')
})

const iconClasses = computed(() => {
  const classes = ['flex-shrink-0']
  
  // Icon size based on button size
  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
    xl: 'w-6 h-6'
  }
  classes.push(iconSizes[props.size])
  
  return classes.join(' ')
})

const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
