{"name": "vue_panel", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "apexcharts": "^4.7.0", "autoprefixer": "^10.4.21", "pinia": "^3.0.2", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue3-apexcharts": "^1.8.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}