import { computed } from 'vue'
import { useMainStore } from '../stores/main'

export function useTheme() {
  const store = useMainStore()

  const isDark = computed(() => store.darkMode)
  
  const toggleTheme = () => {
    store.toggleDarkMode()
  }

  const setTheme = (dark) => {
    if (dark !== store.darkMode) {
      store.toggleDarkMode()
    }
  }

  const initTheme = () => {
    store.initializeDarkMode()
  }

  return {
    isDark,
    toggleTheme,
    setTheme,
    initTheme
  }
}
