<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Cards</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Various card components and layouts</p>
    </div>

    <!-- Basic Cards -->
    <div>
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Basic Cards</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <BaseCard title="Basic Card" subtitle="Simple card with title and subtitle">
          <p class="text-gray-600 dark:text-gray-400">
            This is a basic card component with a title, subtitle, and content area.
          </p>
        </BaseCard>

        <BaseCard title="Card with Actions">
          <template #actions>
            <button class="text-primary-600 dark:text-primary-400 hover:underline text-sm">
              View More
            </button>
            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <EllipsisVerticalIcon class="w-5 h-5" />
            </button>
          </template>
          <p class="text-gray-600 dark:text-gray-400">
            This card includes action buttons in the header area.
          </p>
        </BaseCard>

        <BaseCard title="Card with Footer">
          <p class="text-gray-600 dark:text-gray-400">
            This card demonstrates the footer slot functionality.
          </p>
          <template #footer>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">Last updated: 2 hours ago</span>
              <button class="px-3 py-1 bg-primary-600 text-white rounded text-sm hover:bg-primary-700">
                Update
              </button>
            </div>
          </template>
        </BaseCard>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div>
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Statistics Cards</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Users"
          value="8,549"
          :change="12.5"
          :icon="UsersIcon"
          color="primary"
        />
        <StatsCard
          title="Revenue"
          value="$45,678"
          :change="8.2"
          :icon="CurrencyDollarIcon"
          color="success"
          :gradient="true"
        />
        <StatsCard
          title="Orders"
          value="1,234"
          :change="-2.1"
          :icon="ShoppingBagIcon"
          color="warning"
        />
        <StatsCard
          title="Conversion"
          value="3.2%"
          :change="5.7"
          :icon="ChartBarIcon"
          color="danger"
        />
      </div>
    </div>

    <!-- Advanced Cards -->
    <div>
      <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Advanced Cards</h2>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Chart Card -->
        <BaseCard title="Sales Analytics" subtitle="Monthly performance overview">
          <template #actions>
            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <EllipsisVerticalIcon class="w-5 h-5" />
            </button>
          </template>
          
          <LineChart
            :series="chartData"
            :categories="chartCategories"
            :height="250"
            :colors="['#667eea', '#f093fb']"
          />
          
          <div class="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="text-center">
              <p class="text-lg font-semibold text-gray-900 dark:text-white">$12.5k</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Revenue</p>
            </div>
            <div class="text-center">
              <p class="text-lg font-semibold text-gray-900 dark:text-white">1,234</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Orders</p>
            </div>
            <div class="text-center">
              <p class="text-lg font-semibold text-gray-900 dark:text-white">89%</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Growth</p>
            </div>
          </div>
        </BaseCard>

        <!-- Profile Card -->
        <BaseCard>
          <div class="text-center">
            <img
              src="/avatars/1.png"
              alt="Profile"
              class="w-20 h-20 rounded-full mx-auto mb-4"
            />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">John Doe</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">Senior Developer</p>
            
            <div class="grid grid-cols-3 gap-4 mb-6">
              <div class="text-center">
                <p class="text-lg font-semibold text-gray-900 dark:text-white">23</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Projects</p>
              </div>
              <div class="text-center">
                <p class="text-lg font-semibold text-gray-900 dark:text-white">1.2k</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Followers</p>
              </div>
              <div class="text-center">
                <p class="text-lg font-semibold text-gray-900 dark:text-white">567</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">Following</p>
              </div>
            </div>
            
            <div class="flex space-x-2">
              <button class="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                Follow
              </button>
              <button class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                Message
              </button>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BaseCard from '../../components/ui/BaseCard.vue'
import StatsCard from '../../components/ui/StatsCard.vue'
import LineChart from '../../components/charts/LineChart.vue'
import {
  EllipsisVerticalIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  ChartBarIcon
} from '@heroicons/vue/24/outline'

// Chart data
const chartData = ref([
  {
    name: 'Sales',
    data: [30, 40, 35, 50, 49, 60, 70, 91]
  },
  {
    name: 'Revenue',
    data: [20, 30, 25, 40, 39, 50, 60, 81]
  }
])

const chartCategories = ref(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'])
</script>
