<template>
  <div>
    <apexchart
      :options="chartOptions"
      :series="series"
      :type="type"
      :height="height"
      :width="width"
    />
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { useMainStore } from '../../stores/main'

const props = defineProps({
  series: {
    type: Array,
    required: true
  },
  type: {
    type: String,
    default: 'line'
  },
  height: {
    type: [String, Number],
    default: 350
  },
  width: {
    type: [String, Number],
    default: '100%'
  },
  categories: {
    type: Array,
    default: () => []
  },
  colors: {
    type: Array,
    default: () => ['#667eea', '#764ba2', '#f093fb', '#f5576c']
  },
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  showGrid: {
    type: Boolean,
    default: true
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  showDataLabels: {
    type: <PERSON>olean,
    default: false
  },
  curve: {
    type: String,
    default: 'smooth'
  }
})

const store = useMainStore()

const chartOptions = computed(() => ({
  chart: {
    type: props.type,
    toolbar: {
      show: false
    },
    background: 'transparent',
    fontFamily: 'Inter, ui-sans-serif, system-ui'
  },
  colors: props.colors,
  title: {
    text: props.title,
    style: {
      color: store.darkMode ? '#ffffff' : '#374151',
      fontSize: '16px',
      fontWeight: '600'
    }
  },
  subtitle: {
    text: props.subtitle,
    style: {
      color: store.darkMode ? '#9ca3af' : '#6b7280',
      fontSize: '14px'
    }
  },
  stroke: {
    curve: props.curve,
    width: 3
  },
  grid: {
    show: props.showGrid,
    borderColor: store.darkMode ? '#374151' : '#e5e7eb',
    strokeDashArray: 3
  },
  xaxis: {
    categories: props.categories,
    labels: {
      style: {
        colors: store.darkMode ? '#9ca3af' : '#6b7280'
      }
    },
    axisBorder: {
      color: store.darkMode ? '#374151' : '#e5e7eb'
    },
    axisTicks: {
      color: store.darkMode ? '#374151' : '#e5e7eb'
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: store.darkMode ? '#9ca3af' : '#6b7280'
      }
    }
  },
  legend: {
    show: props.showLegend,
    labels: {
      colors: store.darkMode ? '#ffffff' : '#374151'
    }
  },
  dataLabels: {
    enabled: props.showDataLabels
  },
  tooltip: {
    theme: store.darkMode ? 'dark' : 'light'
  },
  markers: {
    size: 4,
    strokeWidth: 2,
    strokeColors: '#ffffff',
    hover: {
      size: 6
    }
  }
}))

// Watch for dark mode changes and update chart
watch(() => store.darkMode, () => {
  // Force chart re-render by updating a reactive property
}, { immediate: true })
</script>
