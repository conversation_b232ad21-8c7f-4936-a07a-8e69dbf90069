<template>
  <aside 
    class="fixed top-0 left-0 z-50 h-screen bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 sidebar-transition"
    :class="[
      store.sidebarCollapsed 
        ? 'w-16 -translate-x-full lg:translate-x-0' 
        : 'w-64 translate-x-0'
    ]"
  >
    <!-- Logo -->
    <div class="flex items-center justify-center h-16 border-b border-gray-200 dark:border-gray-700">
      <div v-if="!store.sidebarCollapsed" class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-lg">V</span>
        </div>
        <span class="text-xl font-bold text-gray-800 dark:text-white">Vuexy</span>
      </div>
      <div v-else class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
        <span class="text-white font-bold text-lg">V</span>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="mt-4 px-2">
      <div class="space-y-1">
        <!-- Dashboard -->
        <router-link
          to="/"
          class="nav-item"
          :class="{ 'active': $route.name === 'dashboard' }"
        >
          <HomeIcon class="w-5 h-5" />
          <span v-if="!store.sidebarCollapsed" class="ml-3">Dashboard</span>
        </router-link>

        <!-- Apps & Pages -->
        <div class="nav-group">
          <div 
            class="nav-group-header"
            @click="toggleGroup('apps')"
          >
            <div class="flex items-center">
              <CubeIcon class="w-5 h-5" />
              <span v-if="!store.sidebarCollapsed" class="ml-3">Apps & Pages</span>
            </div>
            <ChevronDownIcon 
              v-if="!store.sidebarCollapsed"
              class="w-4 h-4 transition-transform"
              :class="{ 'rotate-180': expandedGroups.apps }"
            />
          </div>
          
          <div 
            v-if="!store.sidebarCollapsed && expandedGroups.apps"
            class="nav-submenu"
          >
            <router-link to="/apps/email" class="nav-subitem">
              <EnvelopeIcon class="w-4 h-4" />
              <span class="ml-2">Email</span>
            </router-link>
            <router-link to="/apps/chat" class="nav-subitem">
              <ChatBubbleLeftRightIcon class="w-4 h-4" />
              <span class="ml-2">Chat</span>
            </router-link>
            <router-link to="/apps/calendar" class="nav-subitem">
              <CalendarIcon class="w-4 h-4" />
              <span class="ml-2">Calendar</span>
            </router-link>
            <router-link to="/apps/kanban" class="nav-subitem">
              <ViewColumnsIcon class="w-4 h-4" />
              <span class="ml-2">Kanban</span>
            </router-link>
          </div>
        </div>

        <!-- eCommerce -->
        <div class="nav-group">
          <div 
            class="nav-group-header"
            @click="toggleGroup('ecommerce')"
          >
            <div class="flex items-center">
              <ShoppingCartIcon class="w-5 h-5" />
              <span v-if="!store.sidebarCollapsed" class="ml-3">eCommerce</span>
            </div>
            <ChevronDownIcon 
              v-if="!store.sidebarCollapsed"
              class="w-4 h-4 transition-transform"
              :class="{ 'rotate-180': expandedGroups.ecommerce }"
            />
          </div>
          
          <div 
            v-if="!store.sidebarCollapsed && expandedGroups.ecommerce"
            class="nav-submenu"
          >
            <router-link to="/ecommerce/dashboard" class="nav-subitem">
              <ChartBarIcon class="w-4 h-4" />
              <span class="ml-2">Dashboard</span>
            </router-link>
            <router-link to="/ecommerce/products" class="nav-subitem">
              <CubeIcon class="w-4 h-4" />
              <span class="ml-2">Products</span>
            </router-link>
            <router-link to="/ecommerce/orders" class="nav-subitem">
              <DocumentTextIcon class="w-4 h-4" />
              <span class="ml-2">Orders</span>
            </router-link>
          </div>
        </div>

        <!-- Users -->
        <div class="nav-group">
          <div 
            class="nav-group-header"
            @click="toggleGroup('users')"
          >
            <div class="flex items-center">
              <UsersIcon class="w-5 h-5" />
              <span v-if="!store.sidebarCollapsed" class="ml-3">Users</span>
            </div>
            <ChevronDownIcon 
              v-if="!store.sidebarCollapsed"
              class="w-4 h-4 transition-transform"
              :class="{ 'rotate-180': expandedGroups.users }"
            />
          </div>
          
          <div 
            v-if="!store.sidebarCollapsed && expandedGroups.users"
            class="nav-submenu"
          >
            <router-link to="/users/list" class="nav-subitem">
              <ListBulletIcon class="w-4 h-4" />
              <span class="ml-2">List</span>
            </router-link>
          </div>
        </div>

        <!-- Components -->
        <div class="nav-group">
          <div 
            class="nav-group-header"
            @click="toggleGroup('components')"
          >
            <div class="flex items-center">
              <PuzzlePieceIcon class="w-5 h-5" />
              <span v-if="!store.sidebarCollapsed" class="ml-3">Components</span>
            </div>
            <ChevronDownIcon 
              v-if="!store.sidebarCollapsed"
              class="w-4 h-4 transition-transform"
              :class="{ 'rotate-180': expandedGroups.components }"
            />
          </div>
          
          <div 
            v-if="!store.sidebarCollapsed && expandedGroups.components"
            class="nav-submenu"
          >
            <router-link to="/components/cards" class="nav-subitem">
              <RectangleStackIcon class="w-4 h-4" />
              <span class="ml-2">Cards</span>
            </router-link>
            <router-link to="/components/buttons" class="nav-subitem">
              <CursorArrowRippleIcon class="w-4 h-4" />
              <span class="ml-2">Buttons</span>
            </router-link>
            <router-link to="/components/modals" class="nav-subitem">
              <Square3Stack3DIcon class="w-4 h-4" />
              <span class="ml-2">Modals</span>
            </router-link>
          </div>
        </div>

        <!-- Forms & Tables -->
        <div class="nav-group">
          <div 
            class="nav-group-header"
            @click="toggleGroup('forms')"
          >
            <div class="flex items-center">
              <DocumentIcon class="w-5 h-5" />
              <span v-if="!store.sidebarCollapsed" class="ml-3">Forms & Tables</span>
            </div>
            <ChevronDownIcon 
              v-if="!store.sidebarCollapsed"
              class="w-4 h-4 transition-transform"
              :class="{ 'rotate-180': expandedGroups.forms }"
            />
          </div>
          
          <div 
            v-if="!store.sidebarCollapsed && expandedGroups.forms"
            class="nav-submenu"
          >
            <router-link to="/forms/basic-inputs" class="nav-subitem">
              <PencilIcon class="w-4 h-4" />
              <span class="ml-2">Basic Inputs</span>
            </router-link>
            <router-link to="/tables/datatables" class="nav-subitem">
              <TableCellsIcon class="w-4 h-4" />
              <span class="ml-2">DataTables</span>
            </router-link>
          </div>
        </div>

        <!-- Charts -->
        <router-link
          to="/charts/apex"
          class="nav-item"
          :class="{ 'active': $route.path.startsWith('/charts') }"
        >
          <ChartBarIcon class="w-5 h-5" />
          <span v-if="!store.sidebarCollapsed" class="ml-3">Charts</span>
        </router-link>
      </div>
    </nav>
  </aside>
</template>

<script setup>
import { ref } from 'vue'
import { useMainStore } from '../../stores/main'
import {
  HomeIcon,
  CubeIcon,
  ShoppingCartIcon,
  UsersIcon,
  PuzzlePieceIcon,
  DocumentIcon,
  ChartBarIcon,
  ChevronDownIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  ViewColumnsIcon,
  DocumentTextIcon,
  ListBulletIcon,
  RectangleStackIcon,
  CursorArrowRippleIcon,
  Square3Stack3DIcon,
  PencilIcon,
  TableCellsIcon
} from '@heroicons/vue/24/outline'

const store = useMainStore()

const expandedGroups = ref({
  apps: true,
  ecommerce: false,
  users: false,
  components: false,
  forms: false
})

const toggleGroup = (group) => {
  if (!store.sidebarCollapsed) {
    expandedGroups.value[group] = !expandedGroups.value[group]
  }
}
</script>

<style scoped>
.nav-item {
  @apply flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200;
}

.nav-item.active {
  @apply bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400;
}

.nav-group-header {
  @apply flex items-center justify-between px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 cursor-pointer;
}

.nav-submenu {
  @apply ml-6 mt-1 space-y-1;
}

.nav-subitem {
  @apply flex items-center px-3 py-2 text-sm text-gray-500 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200;
}

.nav-subitem.router-link-active {
  @apply bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400;
}
</style>
