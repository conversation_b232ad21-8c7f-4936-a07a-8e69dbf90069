<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Basic Tables</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Simple table layouts and styling</p>
    </div>

    <BaseCard title="Basic Tables" subtitle="Coming Soon">
      <div class="text-center py-12">
        <TableCellsIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Basic Tables</h3>
        <p class="text-gray-500 dark:text-gray-400">
          Basic table components are under development. Check back soon!
        </p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import BaseCard from '../../components/ui/BaseCard.vue'
import { TableCellsIcon } from '@heroicons/vue/24/outline'
</script>
