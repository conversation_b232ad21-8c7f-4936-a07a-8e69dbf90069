<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Apex Charts</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Interactive charts and data visualization</p>
    </div>

    <!-- Line Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <BaseCard title="Line Chart" subtitle="Monthly sales data">
        <LineChart
          :series="lineChartData"
          :categories="months"
          :height="300"
          :colors="['#667eea', '#764ba2']"
        />
      </BaseCard>

      <BaseCard title="Area Chart" subtitle="Revenue trends">
        <LineChart
          :series="areaChartData"
          :categories="months"
          :height="300"
          :colors="['#4facfe', '#00f2fe']"
          type="area"
        />
      </BaseCard>
    </div>

    <!-- Donut Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <BaseCard title="Donut Chart" subtitle="Sales by category">
        <DonutChart
          :series="donutData"
          :labels="donutLabels"
          :height="350"
          :colors="['#667eea', '#764ba2', '#f093fb', '#f5576c']"
        />
      </BaseCard>

      <BaseCard title="Pie Chart" subtitle="Market share">
        <DonutChart
          :series="pieData"
          :labels="pieLabels"
          :height="350"
          :colors="['#4facfe', '#00f2fe', '#fa709a', '#fee140']"
          donut-size="0%"
        />
      </BaseCard>
    </div>

    <!-- Bar Charts -->
    <BaseCard title="Bar Chart" subtitle="Quarterly performance">
      <LineChart
        :series="barChartData"
        :categories="quarters"
        :height="400"
        :colors="['#667eea', '#f093fb', '#4facfe']"
        type="bar"
        :show-data-labels="true"
      />
    </BaseCard>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import BaseCard from '../../components/ui/BaseCard.vue'
import LineChart from '../../components/charts/LineChart.vue'
import DonutChart from '../../components/charts/DonutChart.vue'

// Chart data
const months = ref(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])
const quarters = ref(['Q1', 'Q2', 'Q3', 'Q4'])

const lineChartData = ref([
  {
    name: 'Sales',
    data: [30, 40, 35, 50, 49, 60, 70, 91, 125, 140, 120, 100]
  },
  {
    name: 'Revenue',
    data: [20, 30, 25, 40, 39, 50, 60, 81, 105, 120, 100, 80]
  }
])

const areaChartData = ref([
  {
    name: 'Revenue',
    data: [31, 40, 28, 51, 42, 109, 100, 120, 140, 160, 180, 200]
  },
  {
    name: 'Profit',
    data: [11, 32, 45, 32, 34, 52, 41, 60, 80, 100, 120, 140]
  }
])

const barChartData = ref([
  {
    name: 'Product A',
    data: [44, 55, 57, 56]
  },
  {
    name: 'Product B',
    data: [76, 85, 101, 98]
  },
  {
    name: 'Product C',
    data: [35, 41, 36, 26]
  }
])

const donutData = ref([44, 55, 13, 43])
const donutLabels = ref(['Desktop', 'Mobile', 'Tablet', 'Other'])

const pieData = ref([25, 35, 20, 20])
const pieLabels = ref(['Company A', 'Company B', 'Company C', 'Company D'])
</script>
