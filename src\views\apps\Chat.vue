<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Chat</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Real-time messaging application</p>
    </div>

    <BaseCard title="Chat Application" subtitle="Coming Soon">
      <div class="text-center py-12">
        <ChatBubbleLeftRightIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Chat App</h3>
        <p class="text-gray-500 dark:text-gray-400">
          This chat application is under development. Check back soon!
        </p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import BaseCard from '../../components/ui/BaseCard.vue'
import { ChatBubbleLeftRightIcon } from '@heroicons/vue/24/outline'
</script>
