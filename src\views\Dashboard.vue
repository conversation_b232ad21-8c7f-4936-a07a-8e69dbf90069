<template>
  <div class="space-y-6">
    <!-- <PERSON> Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard - Analytics</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Welcome back, {{ store.user.name }}! 👋</p>
    </div>

    <!-- Stats Cards Row 1 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatsCard
        title="Total Sales"
        value="$28,450"
        :change="18.2"
        change-label="vs last month"
        :icon="CurrencyDollarIcon"
        color="primary"
        :gradient="true"
      />
      <StatsCard
        title="Orders"
        value="1,286"
        :change="12.5"
        change-label="vs last month"
        :icon="ShoppingBagIcon"
        color="success"
      />
      <StatsCard
        title="Customers"
        value="4,567"
        :change="8.1"
        change-label="vs last month"
        :icon="UsersIcon"
        color="warning"
      />
      <StatsCard
        title="Revenue"
        value="$42,567"
        :change="-2.4"
        change-label="vs last month"
        :icon="ChartBarIcon"
        color="danger"
      />
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Website Analytics -->
      <BaseCard title="Website Analytics" subtitle="Total 28.5% Conversion Rate">
        <template #actions>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <EllipsisVerticalIcon class="w-5 h-5" />
          </button>
        </template>
        
        <LineChart
          :series="analyticsData"
          :categories="analyticsCategories"
          :colors="['#667eea', '#f093fb']"
          :height="300"
        />
        
        <div class="grid grid-cols-2 gap-4 mt-4">
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">28%</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Sessions</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">1.2k</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">Leads</p>
          </div>
        </div>
      </BaseCard>

      <!-- Sales Overview -->
      <BaseCard title="Sales Overview" subtitle="Monthly Sales Overview">
        <template #actions>
          <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <EllipsisVerticalIcon class="w-5 h-5" />
          </button>
        </template>
        
        <DonutChart
          :series="salesData"
          :labels="salesLabels"
          :colors="['#667eea', '#764ba2', '#f093fb', '#f5576c']"
          :height="300"
        />
      </BaseCard>
    </div>

    <!-- Additional Cards Row -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Earning Reports -->
      <BaseCard title="Earning Reports" subtitle="Weekly Earnings Overview">
        <template #actions>
          <div class="flex space-x-2">
            <button class="text-sm text-primary-600 dark:text-primary-400 hover:underline">
              View More
            </button>
            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <EllipsisVerticalIcon class="w-5 h-5" />
            </button>
          </div>
        </template>
        
        <div class="text-center mb-4">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">$468</p>
          <p class="text-sm text-green-600 font-medium">+4.2%</p>
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            You informed of this week compared to last week
          </p>
        </div>
        
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600 dark:text-gray-400">Earnings</span>
            <span class="font-semibold text-gray-900 dark:text-white">$545.69</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600 dark:text-gray-400">Profit</span>
            <span class="font-semibold text-gray-900 dark:text-white">$256.34</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600 dark:text-gray-400">Expense</span>
            <span class="font-semibold text-gray-900 dark:text-white">$74.19</span>
          </div>
        </div>
      </BaseCard>

      <!-- Support Tracker -->
      <BaseCard title="Support Tracker" subtitle="Last 7 Days">
        <template #actions>
          <div class="flex space-x-2">
            <button class="text-sm text-primary-600 dark:text-primary-400 hover:underline">
              View More
            </button>
            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <EllipsisVerticalIcon class="w-5 h-5" />
            </button>
          </div>
        </template>
        
        <div class="text-center mb-4">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">164</p>
          <p class="text-sm text-gray-500 dark:text-gray-400">Total Tickets</p>
        </div>
        
        <div class="space-y-3">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600 dark:text-gray-400">New Tickets</span>
            <span class="font-semibold text-gray-900 dark:text-white">142</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600 dark:text-gray-400">Open Tickets</span>
            <span class="font-semibold text-gray-900 dark:text-white">28</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600 dark:text-gray-400">Response Time</span>
            <span class="font-semibold text-gray-900 dark:text-white">1 Day</span>
          </div>
        </div>
      </BaseCard>

      <!-- Total Earning -->
      <BaseCard title="Total Earning">
        <template #actions>
          <div class="flex space-x-2">
            <button class="text-sm text-primary-600 dark:text-primary-400 hover:underline">
              View More
            </button>
            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <EllipsisVerticalIcon class="w-5 h-5" />
            </button>
          </div>
        </template>
        
        <div class="text-center mb-4">
          <p class="text-3xl font-bold text-gray-900 dark:text-white">87%</p>
        </div>
        
        <div class="space-y-4">
          <div>
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm text-gray-600 dark:text-gray-400">Total Revenue</span>
              <span class="text-sm font-medium text-green-600">+$126</span>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400">Client Payment</p>
          </div>
          
          <div>
            <div class="flex justify-between items-center mb-1">
              <span class="text-sm text-gray-600 dark:text-gray-400">Total Sales</span>
              <span class="text-sm font-medium text-green-600">+$98</span>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400">Refund</p>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- Projects Table -->
    <BaseCard title="Projects" subtitle="Total 48 projects">
      <template #actions>
        <button class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm">
          Add Project
        </button>
      </template>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Project
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Leader
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Team
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Progress
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="project in projects" :key="project.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-primary-100 dark:bg-primary-900/20 rounded-lg flex items-center justify-center mr-3">
                    <component :is="project.icon" class="w-5 h-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ project.name }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ project.description }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <img class="w-8 h-8 rounded-full mr-2" :src="project.leader.avatar" :alt="project.leader.name" />
                  <span class="text-sm text-gray-900 dark:text-white">{{ project.leader.name }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex -space-x-2">
                  <img
                    v-for="member in project.team.slice(0, 3)"
                    :key="member.id"
                    class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800"
                    :src="member.avatar"
                    :alt="member.name"
                  />
                  <div
                    v-if="project.team.length > 3"
                    class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs font-medium text-gray-600 dark:text-gray-400"
                  >
                    +{{ project.team.length - 3 }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                    <div
                      class="bg-primary-600 h-2 rounded-full"
                      :style="{ width: `${project.progress}%` }"
                    ></div>
                  </div>
                  <span class="text-sm text-gray-600 dark:text-gray-400">{{ project.progress }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <EllipsisVerticalIcon class="w-5 h-5" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useMainStore } from '../stores/main'
import BaseCard from '../components/ui/BaseCard.vue'
import StatsCard from '../components/ui/StatsCard.vue'
import LineChart from '../components/charts/LineChart.vue'
import DonutChart from '../components/charts/DonutChart.vue'
import {
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UsersIcon,
  ChartBarIcon,
  EllipsisVerticalIcon,
  CubeIcon,
  DocumentTextIcon,
  CodeBracketIcon
} from '@heroicons/vue/24/outline'

const store = useMainStore()

// Chart data
const analyticsData = ref([
  {
    name: 'Sessions',
    data: [30, 40, 35, 50, 49, 60, 70, 91, 125, 140, 120, 100]
  },
  {
    name: 'Page Views',
    data: [20, 30, 25, 40, 39, 50, 60, 81, 105, 120, 100, 80]
  }
])

const analyticsCategories = ref([
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
])

const salesData = ref([44, 55, 13, 43])
const salesLabels = ref(['Desktop', 'Mobile', 'Tablet', 'Other'])

// Projects data
const projects = ref([
  {
    id: 1,
    name: 'BGC eCommerce App',
    description: 'React Project',
    icon: CubeIcon,
    leader: {
      name: 'Eileen',
      avatar: 'https://ui-avatars.com/api/?name=Eileen&background=667eea&color=fff'
    },
    team: [
      { id: 1, name: 'John', avatar: 'https://ui-avatars.com/api/?name=John&background=667eea&color=fff' },
      { id: 2, name: 'Jane', avatar: 'https://ui-avatars.com/api/?name=Jane&background=f093fb&color=fff' },
      { id: 3, name: 'Bob', avatar: 'https://ui-avatars.com/api/?name=Bob&background=4facfe&color=fff' },
      { id: 4, name: 'Alice', avatar: 'https://ui-avatars.com/api/?name=Alice&background=fee140&color=000' }
    ],
    progress: 78
  },
  {
    id: 2,
    name: 'Falcon Logo Design',
    description: 'Figma Project',
    icon: DocumentTextIcon,
    leader: {
      name: 'Owen',
      avatar: 'https://ui-avatars.com/api/?name=Owen&background=764ba2&color=fff'
    },
    team: [
      { id: 1, name: 'John', avatar: 'https://ui-avatars.com/api/?name=John&background=667eea&color=fff' },
      { id: 2, name: 'Jane', avatar: 'https://ui-avatars.com/api/?name=Jane&background=f093fb&color=fff' }
    ],
    progress: 18
  },
  {
    id: 3,
    name: 'Dashboard Design',
    description: 'Vue Project',
    icon: CodeBracketIcon,
    leader: {
      name: 'Keith',
      avatar: 'https://ui-avatars.com/api/?name=Keith&background=f5576c&color=fff'
    },
    team: [
      { id: 1, name: 'John', avatar: 'https://ui-avatars.com/api/?name=John&background=667eea&color=fff' },
      { id: 2, name: 'Jane', avatar: 'https://ui-avatars.com/api/?name=Jane&background=f093fb&color=fff' },
      { id: 3, name: 'Bob', avatar: 'https://ui-avatars.com/api/?name=Bob&background=4facfe&color=fff' }
    ],
    progress: 62
  }
])
</script>
