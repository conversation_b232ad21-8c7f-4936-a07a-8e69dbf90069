<template>
  <div 
    class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200"
    :class="[
      shadow ? 'card-shadow' : '',
      padding ? `p-${padding}` : 'p-6',
      customClass
    ]"
  >
    <!-- Header -->
    <div v-if="title || $slots.header" class="flex items-center justify-between mb-4">
      <div v-if="title || $slots.title">
        <slot name="title">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ title }}
          </h3>
        </slot>
        <p v-if="subtitle" class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {{ subtitle }}
        </p>
      </div>
      <div v-if="$slots.actions" class="flex items-center space-x-2">
        <slot name="actions" />
      </div>
    </div>

    <!-- Content -->
    <div :class="contentClass">
      <slot />
    </div>

    <!-- Footer -->
    <div v-if="$slots.footer" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  shadow: {
    type: Boolean,
    default: true
  },
  padding: {
    type: [String, Number],
    default: null
  },
  customClass: {
    type: String,
    default: ''
  },
  contentClass: {
    type: String,
    default: ''
  }
})
</script>
