<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Sidebar -->
    <AppSidebar />
    
    <!-- Main Content Area -->
    <div 
      class="transition-all duration-300 ease-in-out"
      :class="[
        store.sidebarCollapsed 
          ? 'lg:ml-16' 
          : 'lg:ml-64'
      ]"
    >
      <!-- Top Navigation -->
      <AppNavbar />
      
      <!-- Page Content -->
      <main class="p-4 lg:p-6">
        <router-view />
      </main>
      
      <!-- Footer -->
      <AppFooter />
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div 
      v-if="!store.sidebarCollapsed && isMobile"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
      @click="store.setSidebarCollapsed(true)"
    ></div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue'
import { useMainStore } from '../stores/main'
import AppSidebar from '../components/layout/AppSidebar.vue'
import AppNavbar from '../components/layout/AppNavbar.vue'
import AppFooter from '../components/layout/AppFooter.vue'

const store = useMainStore()
const isMobile = ref(false)

const checkMobile = () => {
  isMobile.value = window.innerWidth < 1024
  if (isMobile.value) {
    store.setSidebarCollapsed(true)
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>
