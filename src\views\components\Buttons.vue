<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Buttons</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Various button styles and states</p>
    </div>

    <BaseCard title="Button Components" subtitle="Coming Soon">
      <div class="text-center py-12">
        <CursorArrowRippleIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Button Components</h3>
        <p class="text-gray-500 dark:text-gray-400">
          Button component library is under development. Check back soon!
        </p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import BaseCard from '../../components/ui/BaseCard.vue'
import { CursorArrowRippleIcon } from '@heroicons/vue/24/outline'
</script>
