<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Basic Inputs</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Form input components and layouts</p>
    </div>

    <BaseCard title="Form Components" subtitle="Coming Soon">
      <div class="text-center py-12">
        <PencilIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Form Components</h3>
        <p class="text-gray-500 dark:text-gray-400">
          Form component library is under development. Check back soon!
        </p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import BaseCard from '../../components/ui/BaseCard.vue'
import { PencilIcon } from '@heroicons/vue/24/outline'
</script>
