<template>
  <BaseCard :custom-class="cardClass" :shadow="true">
    <div class="flex items-center justify-between">
      <div>
        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
          {{ title }}
        </p>
        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">
          {{ value }}
        </p>
        <div v-if="change !== undefined" class="flex items-center mt-2">
          <span 
            :class="[
              'text-sm font-medium',
              changeType === 'increase' ? 'text-green-600' : 'text-red-600'
            ]"
          >
            <ArrowUpIcon v-if="changeType === 'increase'" class="w-4 h-4 inline mr-1" />
            <ArrowDownIcon v-else class="w-4 h-4 inline mr-1" />
            {{ Math.abs(change) }}%
          </span>
          <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">
            {{ changeLabel }}
          </span>
        </div>
      </div>
      
      <div v-if="icon" class="flex-shrink-0">
        <div 
          class="w-12 h-12 rounded-lg flex items-center justify-center"
          :class="iconBgClass"
        >
          <component :is="icon" class="w-6 h-6" :class="iconClass" />
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { computed } from 'vue'
import BaseCard from './BaseCard.vue'
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  change: {
    type: Number,
    default: undefined
  },
  changeLabel: {
    type: String,
    default: 'vs last month'
  },
  icon: {
    type: [Object, Function],
    default: null
  },
  color: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  gradient: {
    type: Boolean,
    default: false
  }
})

const changeType = computed(() => {
  if (props.change === undefined) return null
  return props.change >= 0 ? 'increase' : 'decrease'
})

const cardClass = computed(() => {
  if (props.gradient) {
    return `gradient-${props.color} text-white`
  }
  return ''
})

const iconBgClass = computed(() => {
  if (props.gradient) {
    return 'bg-white bg-opacity-20'
  }
  
  const colorMap = {
    primary: 'bg-primary-100 dark:bg-primary-900/20',
    success: 'bg-green-100 dark:bg-green-900/20',
    warning: 'bg-yellow-100 dark:bg-yellow-900/20',
    danger: 'bg-red-100 dark:bg-red-900/20',
    info: 'bg-blue-100 dark:bg-blue-900/20'
  }
  
  return colorMap[props.color] || colorMap.primary
})

const iconClass = computed(() => {
  if (props.gradient) {
    return 'text-white'
  }
  
  const colorMap = {
    primary: 'text-primary-600 dark:text-primary-400',
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    danger: 'text-red-600 dark:text-red-400',
    info: 'text-blue-600 dark:text-blue-400'
  }
  
  return colorMap[props.color] || colorMap.primary
})
</script>
