<template>
  <div>
    <apexchart
      :options="chartOptions"
      :series="series"
      type="donut"
      :height="height"
      :width="width"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useMainStore } from '../../stores/main'

const props = defineProps({
  series: {
    type: Array,
    required: true
  },
  labels: {
    type: Array,
    required: true
  },
  height: {
    type: [String, Number],
    default: 300
  },
  width: {
    type: [String, Number],
    default: '100%'
  },
  colors: {
    type: Array,
    default: () => ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe']
  },
  showLegend: {
    type: Boolean,
    default: true
  },
  showDataLabels: {
    type: Boolean,
    default: true
  },
  donutSize: {
    type: String,
    default: '70%'
  }
})

const store = useMainStore()

const chartOptions = computed(() => ({
  chart: {
    type: 'donut',
    background: 'transparent',
    fontFamily: 'Inter, ui-sans-serif, system-ui'
  },
  colors: props.colors,
  labels: props.labels,
  legend: {
    show: props.showLegend,
    position: 'bottom',
    labels: {
      colors: store.darkMode ? '#ffffff' : '#374151'
    }
  },
  dataLabels: {
    enabled: props.showDataLabels,
    style: {
      colors: ['#ffffff']
    }
  },
  plotOptions: {
    pie: {
      donut: {
        size: props.donutSize,
        labels: {
          show: true,
          total: {
            show: true,
            label: 'Total',
            color: store.darkMode ? '#ffffff' : '#374151',
            formatter: function (w) {
              return w.globals.seriesTotals.reduce((a, b) => {
                return a + b
              }, 0)
            }
          },
          value: {
            color: store.darkMode ? '#ffffff' : '#374151',
            fontSize: '24px',
            fontWeight: '600'
          },
          name: {
            color: store.darkMode ? '#9ca3af' : '#6b7280'
          }
        }
      }
    }
  },
  tooltip: {
    theme: store.darkMode ? 'dark' : 'light'
  },
  responsive: [{
    breakpoint: 480,
    options: {
      chart: {
        width: 200
      },
      legend: {
        position: 'bottom'
      }
    }
  }]
}))
</script>
