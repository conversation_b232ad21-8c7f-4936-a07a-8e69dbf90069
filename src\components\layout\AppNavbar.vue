<template>
  <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 h-16">
    <div class="flex items-center justify-between h-full px-4 lg:px-6">
      <!-- Left Side -->
      <div class="flex items-center space-x-4">
        <!-- Sidebar Toggle -->
        <button
          @click="store.toggleSidebar()"
          class="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
        >
          <Bars3Icon class="w-5 h-5" />
        </button>

        <!-- Search -->
        <div class="relative hidden md:block">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search..."
            class="block w-64 pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
          />
        </div>
      </div>

      <!-- Right Side -->
      <div class="flex items-center space-x-2">
        <!-- Language Selector -->
        <Menu as="div" class="relative">
          <MenuButton class="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
            <LanguageIcon class="w-5 h-5" />
          </MenuButton>
          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <MenuItems class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 focus:outline-none z-50">
              <div class="py-1">
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'block px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    English
                  </a>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'block px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    French
                  </a>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'block px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    German
                  </a>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>

        <!-- Theme Toggle -->
        <button
          @click="toggleTheme"
          class="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
        >
          <SunIcon v-if="isDark" class="w-5 h-5" />
          <MoonIcon v-else class="w-5 h-5" />
        </button>

        <!-- Shortcuts -->
        <button class="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
          <CommandLineIcon class="w-5 h-5" />
        </button>

        <!-- Notifications -->
        <Menu as="div" class="relative">
          <MenuButton class="relative p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
            <BellIcon class="w-5 h-5" />
            <span
              v-if="store.unreadCount > 0"
              class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full"
            >
              {{ store.unreadCount }}
            </span>
          </MenuButton>
          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <MenuItems class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 focus:outline-none z-50">
              <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
                  <span class="text-sm text-gray-500 dark:text-gray-400">{{ store.unreadCount }} New</span>
                </div>
              </div>
              <div class="max-h-64 overflow-y-auto">
                <div
                  v-for="notification in store.notifications.slice(0, 5)"
                  :key="notification.id"
                  class="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                  @click="store.markNotificationAsRead(notification.id)"
                >
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <img
                        v-if="notification.avatar"
                        :src="notification.avatar"
                        :alt="notification.title"
                        class="w-8 h-8 rounded-full"
                      />
                      <div
                        v-else
                        class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white text-sm font-medium"
                      >
                        {{ notification.initials || notification.title.charAt(0) }}
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 dark:text-white">
                        {{ notification.title }}
                      </p>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ notification.message }}
                      </p>
                      <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        {{ notification.time }}
                      </p>
                    </div>
                    <div v-if="!notification.read" class="w-2 h-2 bg-primary-500 rounded-full"></div>
                  </div>
                </div>
              </div>
              <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  @click="store.markAllNotificationsAsRead()"
                  class="w-full text-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                >
                  View all notifications
                </button>
              </div>
            </MenuItems>
          </transition>
        </Menu>

        <!-- User Menu -->
        <Menu as="div" class="relative">
          <MenuButton class="flex items-center space-x-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200">
            <img
              :src="store.user.avatar"
              :alt="store.user.name"
              class="w-8 h-8 rounded-full"
            />
          </MenuButton>
          <transition
            enter-active-class="transition duration-100 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <MenuItems class="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 focus:outline-none z-50">
              <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                  <img
                    :src="store.user.avatar"
                    :alt="store.user.name"
                    class="w-10 h-10 rounded-full"
                  />
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ store.user.name }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ store.user.role }}</p>
                  </div>
                </div>
              </div>
              <div class="py-1">
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    <UserIcon class="w-4 h-4 mr-3" />
                    My Profile
                  </a>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    <CogIcon class="w-4 h-4 mr-3" />
                    Settings
                  </a>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    <CreditCardIcon class="w-4 h-4 mr-3" />
                    Billing
                    <span class="ml-auto bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full">4</span>
                  </a>
                </MenuItem>
                <div class="border-t border-gray-200 dark:border-gray-700"></div>
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    <QuestionMarkCircleIcon class="w-4 h-4 mr-3" />
                    FAQ
                  </a>
                </MenuItem>
                <MenuItem v-slot="{ active }">
                  <a
                    href="#"
                    :class="[
                      active ? 'bg-gray-100 dark:bg-gray-700' : '',
                      'flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300'
                    ]"
                  >
                    <ArrowRightOnRectangleIcon class="w-4 h-4 mr-3" />
                    Logout
                  </a>
                </MenuItem>
              </div>
            </MenuItems>
          </transition>
        </Menu>
      </div>
    </div>
  </header>
</template>

<script setup>
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import { useMainStore } from '../../stores/main'
import { useTheme } from '../../composables/useTheme'
import {
  Bars3Icon,
  MagnifyingGlassIcon,
  LanguageIcon,
  SunIcon,
  MoonIcon,
  CommandLineIcon,
  BellIcon,
  UserIcon,
  CogIcon,
  CreditCardIcon,
  QuestionMarkCircleIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

const store = useMainStore()
const { isDark, toggleTheme } = useTheme()
</script>
