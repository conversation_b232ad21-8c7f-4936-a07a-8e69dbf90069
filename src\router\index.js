import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: () => import('../views/Dashboard.vue'),
      meta: { title: 'Dashboard - Analytics' }
    },
    {
      path: '/apps',
      name: 'apps',
      children: [
        {
          path: 'email',
          name: 'email',
          component: () => import('../views/apps/Email.vue'),
          meta: { title: 'Email' }
        },
        {
          path: 'chat',
          name: 'chat',
          component: () => import('../views/apps/Chat.vue'),
          meta: { title: 'Chat' }
        },
        {
          path: 'calendar',
          name: 'calendar',
          component: () => import('../views/apps/Calendar.vue'),
          meta: { title: 'Calendar' }
        },
        {
          path: 'kanban',
          name: 'kanban',
          component: () => import('../views/apps/Kanban.vue'),
          meta: { title: 'Kanban' }
        }
      ]
    },
    {
      path: '/ecommerce',
      name: 'ecommerce',
      children: [
        {
          path: 'dashboard',
          name: 'ecommerce-dashboard',
          component: () => import('../views/ecommerce/Dashboard.vue'),
          meta: { title: 'eCommerce Dashboard' }
        },
        {
          path: 'products',
          name: 'products',
          component: () => import('../views/ecommerce/Products.vue'),
          meta: { title: 'Products' }
        },
        {
          path: 'orders',
          name: 'orders',
          component: () => import('../views/ecommerce/Orders.vue'),
          meta: { title: 'Orders' }
        }
      ]
    },
    {
      path: '/users',
      name: 'users',
      children: [
        {
          path: 'list',
          name: 'users-list',
          component: () => import('../views/users/UsersList.vue'),
          meta: { title: 'Users List' }
        },
        {
          path: 'view/:id',
          name: 'user-view',
          component: () => import('../views/users/UserView.vue'),
          meta: { title: 'User Details' }
        }
      ]
    },
    {
      path: '/components',
      name: 'components',
      children: [
        {
          path: 'cards',
          name: 'cards',
          component: () => import('../views/components/Cards.vue'),
          meta: { title: 'Cards' }
        },
        {
          path: 'buttons',
          name: 'buttons',
          component: () => import('../views/components/Buttons.vue'),
          meta: { title: 'Buttons' }
        },
        {
          path: 'modals',
          name: 'modals',
          component: () => import('../views/components/Modals.vue'),
          meta: { title: 'Modals' }
        }
      ]
    },
    {
      path: '/forms',
      name: 'forms',
      children: [
        {
          path: 'basic-inputs',
          name: 'basic-inputs',
          component: () => import('../views/forms/BasicInputs.vue'),
          meta: { title: 'Basic Inputs' }
        },
        {
          path: 'form-layouts',
          name: 'form-layouts',
          component: () => import('../views/forms/FormLayouts.vue'),
          meta: { title: 'Form Layouts' }
        }
      ]
    },
    {
      path: '/tables',
      name: 'tables',
      children: [
        {
          path: 'basic',
          name: 'tables-basic',
          component: () => import('../views/tables/BasicTables.vue'),
          meta: { title: 'Basic Tables' }
        },
        {
          path: 'datatables',
          name: 'datatables',
          component: () => import('../views/tables/DataTables.vue'),
          meta: { title: 'DataTables' }
        }
      ]
    },
    {
      path: '/charts',
      name: 'charts',
      children: [
        {
          path: 'apex',
          name: 'apex-charts',
          component: () => import('../views/charts/ApexCharts.vue'),
          meta: { title: 'Apex Charts' }
        }
      ]
    }
  ]
})

// Update document title based on route meta
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} | Vuexy Admin` : 'Vuexy Admin'
  next()
})

export default router
