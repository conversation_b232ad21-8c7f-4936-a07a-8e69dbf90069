# Vuexy Admin Dashboard - Vue 3

A comprehensive Vue.js admin dashboard application that replicates the design and functionality of the Vuexy admin template. Built with modern technologies and best practices.

![Vue 3](https://img.shields.io/badge/Vue-3.5.13-4FC08D?style=flat&logo=vue.js&logoColor=white)
![Vite](https://img.shields.io/badge/Vite-6.2.4-646CFF?style=flat&logo=vite&logoColor=white)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.0-38B2AC?style=flat&logo=tailwind-css&logoColor=white)
![Pinia](https://img.shields.io/badge/Pinia-2.2.0-FFD859?style=flat&logo=pinia&logoColor=black)

## ✨ Features

### 🎨 Modern UI Components
- **Responsive Design**: Mobile-first approach with tablet and desktop optimizations
- **Dark/Light Mode**: Automatic theme switching with system preference detection
- **Component Library**: Comprehensive set of reusable UI components
- **Icons**: Heroicons integration for consistent iconography
- **Typography**: Inter font family for modern, clean text rendering

### 📊 Dashboard & Analytics
- **Statistics Cards**: Animated cards with trend indicators
- **Interactive Charts**: ApexCharts integration for data visualization
- **Real-time Data**: Mock data with realistic business metrics
- **Responsive Tables**: Sortable and filterable data tables
- **Progress Indicators**: Visual progress bars and completion status

### 🧭 Navigation & Layout
- **Collapsible Sidebar**: Smooth animations with mobile responsiveness
- **Breadcrumb Navigation**: Clear page hierarchy indication
- **Multi-level Menus**: Nested navigation with expand/collapse functionality
- **Search Integration**: Global search functionality in top navigation
- **User Profile**: Dropdown menu with user actions and settings

### 🔧 Technical Features
- **Vue 3 Composition API**: Modern reactive programming patterns
- **Pinia State Management**: Centralized application state
- **Vue Router**: Client-side routing with lazy loading
- **TypeScript Ready**: Prepared for TypeScript integration
- **Hot Module Replacement**: Fast development with Vite

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vue_panel
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## 📁 Project Structure

```
src/
├── assets/              # Static assets (CSS, images)
├── components/          # Reusable Vue components
│   ├── charts/         # Chart components (ApexCharts)
│   ├── layout/         # Layout components (Sidebar, Navbar, Footer)
│   └── ui/             # UI components (Cards, Buttons, etc.)
├── composables/        # Vue composables for shared logic
├── layouts/            # Page layout templates
├── router/             # Vue Router configuration
├── stores/             # Pinia stores for state management
├── views/              # Page components
│   ├── apps/          # Application pages (Email, Chat, etc.)
│   ├── charts/        # Chart demonstration pages
│   ├── components/    # Component showcase pages
│   ├── ecommerce/     # E-commerce related pages
│   ├── forms/         # Form examples and layouts
│   ├── tables/        # Table examples
│   └── users/         # User management pages
└── main.js             # Application entry point
```

## 🎯 Available Pages

### 📈 Dashboards
- **Analytics Dashboard**: Main dashboard with KPIs and charts
- **eCommerce Dashboard**: Online store metrics (Coming Soon)
- **CRM Dashboard**: Customer relationship management (Coming Soon)

### 📱 Applications
- **Email**: Email management interface (Coming Soon)
- **Chat**: Real-time messaging (Coming Soon)
- **Calendar**: Event scheduling (Coming Soon)
- **Kanban**: Project management boards (Coming Soon)

### 🛍️ eCommerce
- **Products**: Product catalog management (Coming Soon)
- **Orders**: Order tracking and management (Coming Soon)
- **Customers**: Customer database (Coming Soon)

### 👥 User Management
- **Users List**: User directory and management (Coming Soon)
- **User Details**: Individual user profiles (Coming Soon)
- **Roles & Permissions**: Access control (Coming Soon)

### 🧩 UI Components
- **Cards**: Various card layouts and styles
- **Buttons**: Button variations and states (Coming Soon)
- **Modals**: Dialog and overlay components (Coming Soon)
- **Forms**: Input fields and form layouts (Coming Soon)
- **Tables**: Data table examples (Coming Soon)

### 📊 Charts & Visualization
- **ApexCharts**: Line, area, bar, and donut charts
- **Interactive Features**: Zoom, pan, and hover effects
- **Responsive Design**: Mobile-optimized chart layouts

## 🛠️ Technology Stack

### Core Framework
- **Vue 3**: Progressive JavaScript framework with Composition API
- **Vite**: Next-generation frontend build tool
- **Vue Router 4**: Official router for Vue.js applications

### State Management
- **Pinia**: Intuitive, type-safe store for Vue

### Styling & UI
- **Tailwind CSS**: Utility-first CSS framework
- **Headless UI**: Unstyled, accessible UI components
- **Heroicons**: Beautiful hand-crafted SVG icons

### Charts & Visualization
- **ApexCharts**: Modern charting library
- **Vue3-ApexCharts**: Vue 3 wrapper for ApexCharts

### Development Tools
- **PostCSS**: CSS post-processor
- **Autoprefixer**: CSS vendor prefixing
- **Vue DevTools**: Browser extension for debugging

## 🎨 Customization

### Theme Configuration
The application supports extensive theming through Tailwind CSS configuration:

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          // Custom primary color palette
        },
        secondary: {
          // Custom secondary color palette
        }
      }
    }
  }
}
```

### Component Customization
All components are built with customization in mind:

```vue
<StatsCard
  title="Custom Metric"
  value="$1,234"
  :change="15.2"
  :icon="CustomIcon"
  color="primary"
  :gradient="true"
/>
```

### Dark Mode
Dark mode is automatically handled through the theme system:

```javascript
// Toggle dark mode
const { toggleTheme } = useTheme()
toggleTheme()
```

## 📱 Responsive Design

The dashboard is fully responsive with breakpoints:
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

Key responsive features:
- Collapsible sidebar on mobile
- Stacked cards on smaller screens
- Responsive navigation menus
- Touch-friendly interactions

## 🔧 Configuration

### Environment Variables
Create a `.env` file for environment-specific configuration:

```env
VITE_APP_TITLE=Vuexy Admin Dashboard
VITE_API_BASE_URL=https://api.example.com
```

### Router Configuration
Add new routes in `src/router/index.js`:

```javascript
{
  path: '/custom-page',
  name: 'custom-page',
  component: () => import('../views/CustomPage.vue'),
  meta: { title: 'Custom Page' }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Pixinvent**: Original Vuexy template design inspiration
- **Vue.js Team**: For the amazing Vue 3 framework
- **Tailwind CSS**: For the utility-first CSS framework
- **Heroicons**: For the beautiful icon set
- **ApexCharts**: For the interactive charting library

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review existing issues for solutions

---

**Built with ❤️ using Vue 3, Vite, and Tailwind CSS**
